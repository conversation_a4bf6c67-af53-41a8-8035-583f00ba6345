#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import tkinter as tk
from database import DatabaseManager
from gui.maintenance_form import MaintenanceForm
import traceback
from datetime import datetime

def test_maintenance_fixed():
    """Testa o formulário de manutenção corrigido"""
    
    print("=== TESTE DO FORMULÁRIO CORRIGIDO ===")
    
    # Inicializar database
    db_manager = DatabaseManager()
    
    try:
        # Verificar usuário
        users = db_manager.execute_query("SELECT * FROM users LIMIT 1")
        if not users:
            print("❌ Nenhum usuário encontrado")
            return
        
        user_id = users[0]['id']
        print(f"✓ Usuário encontrado: ID {user_id}")
        
        # Verificar veículos
        vehicles = db_manager.get_user_vehicles(user_id)
        if not vehicles:
            print("❌ Nenhum veículo encontrado")
            return
        
        vehicle = vehicles[0]
        print(f"✓ Veículo encontrado: {vehicle['name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.withdraw()  # Esconder janela principal
        
        # Variável para capturar callback
        callback_executed = False
        
        def test_callback():
            nonlocal callback_executed
            callback_executed = True
            print("✅ [CALLBACK] Executado com sucesso!")
        
        # Criar formulário
        print("🔄 Criando formulário corrigido...")
        form = MaintenanceForm(root, db_manager, user_id, callback=test_callback)
        
        # Preencher campos
        print("🔄 Preenchendo campos...")
        
        # Selecionar veículo
        vehicle_name = f"{vehicle['name']} - {vehicle['brand']} {vehicle['model']}"
        form.vehicle_var.set(vehicle_name)
        print(f"✓ Veículo: {vehicle_name}")
        
        # Tipo de manutenção
        form.maintenance_type_var.set('oil_change')
        print("✓ Tipo: oil_change")
        
        # Descrição
        form.description_text.insert('1.0', 'Teste do formulário corrigido')
        print("✓ Descrição preenchida")
        
        # Data (formato brasileiro)
        form.service_date_var.set('17/07/2025')
        print("✓ Data: 17/07/2025")
        
        # Custo
        form.cost_var.set('199.90')
        print("✓ Custo: R$ 199,90")
        
        # Campos opcionais
        form.mileage_var.set('30000')
        form.service_provider_var.set('Oficina Corrigida')
        form.receipt_number_var.set('REC-CORRIGIDO-001')
        form.notes_text.insert('1.0', 'Teste após correções')
        
        print("✓ Campos opcionais preenchidos")
        
        # Contar manutenções antes
        count_before = db_manager.execute_query(
            "SELECT COUNT(*) as count FROM vehicle_maintenance WHERE user_id = ?",
            (user_id,)
        )[0]['count']
        print(f"📊 Manutenções antes: {count_before}")
        
        # Executar salvamento
        print("🔄 Executando salvamento com debug...")
        print("=" * 50)
        
        # Chamar save() que agora tem debug
        form.save()
        
        print("=" * 50)
        print("🔄 Salvamento concluído")
        
        # Verificar resultado
        count_after = db_manager.execute_query(
            "SELECT COUNT(*) as count FROM vehicle_maintenance WHERE user_id = ?",
            (user_id,)
        )[0]['count']
        print(f"📊 Manutenções depois: {count_after}")
        
        if count_after > count_before:
            print("✅ MANUTENÇÃO SALVA COM SUCESSO!")
            
            # Verificar dados salvos
            last_maintenance = db_manager.execute_query(
                "SELECT * FROM vehicle_maintenance WHERE user_id = ? ORDER BY id DESC LIMIT 1",
                (user_id,)
            )[0]
            
            print("📋 Dados salvos:")
            print(f"   ID: {last_maintenance['id']}")
            print(f"   Veículo: {last_maintenance['vehicle_id']}")
            print(f"   Tipo: {last_maintenance['maintenance_type']}")
            print(f"   Descrição: {last_maintenance['description']}")
            print(f"   Data: {last_maintenance['service_date']}")
            print(f"   Custo: R$ {last_maintenance['cost']}")
            
        else:
            print("❌ MANUTENÇÃO NÃO FOI SALVA!")
        
        # Verificar callback
        if callback_executed:
            print("✅ Callback executado corretamente!")
        else:
            print("❌ Callback NÃO foi executado!")
        
        # Limpar
        try:
            root.destroy()
        except:
            pass
        
        print("\n✅ TESTE FINALIZADO!")
        
    except Exception as e:
        print(f"\n❌ ERRO NO TESTE:")
        print(f"Tipo: {type(e).__name__}")
        print(f"Mensagem: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    test_maintenance_fixed()
