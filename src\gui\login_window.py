#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Janela de login do sistema
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font as tkFont

class LoginWindow:
    def __init__(self, parent, auth_manager, success_callback):
        self.parent = parent
        self.auth_manager = auth_manager
        self.success_callback = success_callback
        
        # Criar janela de login
        self.window = tk.Toplevel(parent)
        self.window.title("Sistema de Gestão Financeira - Login")
        self.window.geometry("400x500")
        self.window.resizable(False, False)
        
        # Centralizar janela
        self.center_window()
        
        # Configurar janela
        self.window.transient(parent)
        self.window.grab_set()
        
        # Criar interface
        self.create_widgets()
        
        # Focar no campo de usuário
        self.username_entry.focus()
        
        # Bind Enter para fazer login
        self.window.bind('<Return>', lambda e: self.login())
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """Cria os widgets da interface"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title_font = tkFont.Font(family="Arial", size=15, weight="bold")
        title_label = ttk.Label(main_frame, text="Sistema de Gestão Financeira", font=title_font)
        title_label.pack(pady=(0, 20))
        
        # Subtítulo
        subtitle_label = ttk.Label(main_frame, text="Faça login para continuar", 
                                 font=("Arial", 11,), foreground="blue")
        subtitle_label.pack(pady=(0, 30))
        
        # Frame do formulário
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Campo usuário
        ttk.Label(form_frame, text="Usuário:").pack(anchor=tk.W, pady=(0, 10))
        self.username_entry = ttk.Entry(form_frame, font=("Arial", 10))
        self.username_entry.pack(fill=tk.X, pady=(0, 10))
        
        # Campo senha
        ttk.Label(form_frame, text="Senha:").pack(anchor=tk.W, pady=(0, 10))
        self.password_entry = ttk.Entry(form_frame, show="*", font=("Arial", 11))
        self.password_entry.pack(fill=tk.X, pady=(0, 10))
        
        # Botão de login
        login_btn = ttk.Button(form_frame, text="Entrar", command=self.login)
        login_btn.pack(fill=tk.X, pady=(0, 20))
        
        # Separador
        separator = ttk.Separator(form_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=20)
        
        # Botão de registro
        register_btn = ttk.Button(form_frame, text="Criar Nova Conta", 
                                command=self.show_register)
        register_btn.pack(fill=tk.X)
        
        # Frame de informações
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Informações de login padrão
        info_text = """Usuário padrão do sistema:
Login: admin
Senha: admin123
Altere a senha do usuário padrão após o primeiro login."""
        
        info_label = ttk.Label(info_frame, text=info_text, 
                             font=("Arial", 10), foreground="green")
        info_label.pack()
    
    def login(self):
        """Realiza o login"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror("Erro", "Por favor, preencha todos os campos")
            return
        
        try:
            user_data = self.auth_manager.authenticate_user(username, password)
            
            if user_data:
                # Login bem-sucedido
                self.window.destroy()
                self.success_callback(user_data)
            else:
                messagebox.showerror("Erro", "Usuário ou senha incorretos")
                self.password_entry.delete(0, tk.END)
                self.password_entry.focus()
                
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao fazer login: {str(e)}")
    
    def show_register(self):
        """Mostra janela de registro"""
        RegisterWindow(self.window, self.auth_manager, self.on_register_success)
    
    def on_register_success(self):
        """Callback executado após registro bem-sucedido"""
        messagebox.showinfo("Sucesso", "Conta criada com sucesso! Faça login para continuar.")

class RegisterWindow:
    def __init__(self, parent, auth_manager, success_callback):
        self.parent = parent
        self.auth_manager = auth_manager
        self.success_callback = success_callback
        
        # Criar janela de registro
        self.window = tk.Toplevel(parent)
        self.window.title("Criar Nova Conta")
        self.window.geometry("450x600")
        self.window.resizable(False, False)
        
        # Centralizar janela
        self.center_window()
        
        # Configurar janela
        self.window.transient(parent)
        self.window.grab_set()
        
        # Criar interface
        self.create_widgets()
        
        # Focar no primeiro campo
        self.full_name_entry.focus()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """Cria os widgets da interface"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title_font = tkFont.Font(family="Arial", size=16, weight="bold")
        title_label = ttk.Label(main_frame, text="Criar Nova Conta", font=title_font)
        title_label.pack(pady=(0, 30))
        
        # Frame do formulário
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Campo nome completo
        ttk.Label(form_frame, text="Nome Completo:").pack(anchor=tk.W, pady=(0, 5))
        self.full_name_entry = ttk.Entry(form_frame, font=("Arial", 11))
        self.full_name_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Campo usuário
        ttk.Label(form_frame, text="Nome de Usuário:").pack(anchor=tk.W, pady=(0, 5))
        self.username_entry = ttk.Entry(form_frame, font=("Arial", 11))
        self.username_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Campo email
        ttk.Label(form_frame, text="Email:").pack(anchor=tk.W, pady=(0, 5))
        self.email_entry = ttk.Entry(form_frame, font=("Arial", 11))
        self.email_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Campo senha
        ttk.Label(form_frame, text="Senha:").pack(anchor=tk.W, pady=(0, 5))
        self.password_entry = ttk.Entry(form_frame, show="*", font=("Arial", 11))
        self.password_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Campo confirmar senha
        ttk.Label(form_frame, text="Confirmar Senha:").pack(anchor=tk.W, pady=(0, 5))
        self.confirm_password_entry = ttk.Entry(form_frame, show="*", font=("Arial", 11))
        self.confirm_password_entry.pack(fill=tk.X, pady=(0, 20))
        
        # Frame dos botões
        button_frame = ttk.Frame(form_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Botão cancelar
        cancel_btn = ttk.Button(button_frame, text="Cancelar", command=self.window.destroy)
        cancel_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Botão criar conta
        create_btn = ttk.Button(button_frame, text="Criar Conta", command=self.create_account)
        create_btn.pack(side=tk.RIGHT)
    
    def create_account(self):
        """Cria nova conta de usuário"""
        full_name = self.full_name_entry.get().strip()
        username = self.username_entry.get().strip()
        email = self.email_entry.get().strip()
        password = self.password_entry.get()
        confirm_password = self.confirm_password_entry.get()
        
        # Validações
        if not all([full_name, username, email, password, confirm_password]):
            messagebox.showerror("Erro", "Por favor, preencha todos os campos")
            return
        
        if password != confirm_password:
            messagebox.showerror("Erro", "As senhas não coincidem")
            return
        
        try:
            self.auth_manager.create_user(
                username=username,
                password=password,
                email=email,
                full_name=full_name,
                is_admin=False
            )
            
            self.window.destroy()
            self.success_callback()
            
        except Exception as e:
            messagebox.showerror("Erro", str(e))
