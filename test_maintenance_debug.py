#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
import traceback

def test_maintenance_save():
    """Testa o salvamento de manutenção para identificar o erro"""
    
    print("=== TESTE DE SALVAMENTO DE MANUTENÇÃO ===")
    
    # Inicializar database
    db_manager = DatabaseManager()
    
    try:
        # Primeiro, vamos verificar se existem usuários
        users = db_manager.execute_query("SELECT * FROM users LIMIT 1")
        if not users:
            print("❌ Nenhum usuário encontrado. Criando usuário de teste...")
            # Criar usuário de teste
            db_manager.execute_query(
                "INSERT INTO users (username, password_hash, email) VALUES (?, ?, ?)",
                ("teste", "hash_teste", "<EMAIL>")
            )
            users = db_manager.execute_query("SELECT * FROM users LIMIT 1")
        
        user_id = users[0]['id']
        print(f"✓ Usuário encontrado: ID {user_id}")
        
        # Verificar se existem veículos
        vehicles = db_manager.get_user_vehicles(user_id)
        if not vehicles:
            print("❌ Nenhum veículo encontrado. Criando veículo de teste...")
            vehicle_data = {
                'name': 'Carro Teste',
                'brand': 'Toyota',
                'model': 'Corolla',
                'year': 2020,
                'license_plate': 'TST-1234',
                'color': 'Branco',
                'fuel_type': 'flex',
                'engine_size': '1.8',
                'mileage': 50000
            }
            db_manager.add_vehicle(user_id, vehicle_data)
            vehicles = db_manager.get_user_vehicles(user_id)
        
        vehicle_id = vehicles[0]['id']
        print(f"✓ Veículo encontrado: ID {vehicle_id} - {vehicles[0]['name']}")
        
        # Dados de teste para manutenção
        maintenance_data = {
            'vehicle_id': vehicle_id,
            'maintenance_type': 'oil_change',
            'description': 'Troca de óleo e filtro - TESTE',
            'service_date': '2024-01-15',
            'mileage_at_service': 50000,
            'cost': 150.00,
            'service_provider': 'Oficina Teste',
            'next_service_date': '2024-07-15',
            'next_service_mileage': 55000,
            'warranty_period': 6,
            'warranty_expiry': '2024-07-15',
            'receipt_number': 'REC-TESTE-001',
            'notes': 'Teste de salvamento de manutenção',
            'is_scheduled': False,
            'is_completed': True
        }
        
        print("\n=== TENTANDO SALVAR MANUTENÇÃO ===")
        print("Dados da manutenção:")
        for key, value in maintenance_data.items():
            print(f"  {key}: {value}")
        
        # Tentar salvar manutenção
        maintenance_id = db_manager.add_maintenance_record(user_id, maintenance_data)
        print(f"✓ Manutenção salva com sucesso! ID: {maintenance_id}")
        
        # Verificar se foi salva corretamente
        saved_maintenance = db_manager.execute_query(
            "SELECT * FROM vehicle_maintenance WHERE id = ?",
            (maintenance_id,)
        )
        
        if saved_maintenance:
            print("✓ Manutenção encontrada no banco de dados:")
            maintenance = saved_maintenance[0]
            print(f"  ID: {maintenance['id']}")
            print(f"  Veículo ID: {maintenance['vehicle_id']}")
            print(f"  Tipo: {maintenance['maintenance_type']}")
            print(f"  Descrição: {maintenance['description']}")
            print(f"  Data: {maintenance['service_date']}")
            print(f"  Custo: {maintenance['cost']}")
        else:
            print("❌ Manutenção não encontrada no banco de dados!")
        
        print("\n✅ TESTE CONCLUÍDO COM SUCESSO!")
        
    except Exception as e:
        print(f"\n❌ ERRO DURANTE O TESTE:")
        print(f"Tipo do erro: {type(e).__name__}")
        print(f"Mensagem: {str(e)}")
        print("\nTraceback completo:")
        traceback.print_exc()
        
        # Verificar se a tabela existe
        try:
            tables = db_manager.execute_query(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='vehicle_maintenance'"
            )
            if tables:
                print("\n✓ Tabela vehicle_maintenance existe")
                
                # Verificar estrutura da tabela
                columns = db_manager.execute_query("PRAGMA table_info(vehicle_maintenance)")
                print("Colunas da tabela:")
                for col in columns:
                    print(f"  {col['name']}: {col['type']}")
            else:
                print("\n❌ Tabela vehicle_maintenance NÃO existe!")
        except Exception as table_error:
            print(f"\n❌ Erro ao verificar tabela: {table_error}")

if __name__ == "__main__":
    test_maintenance_save()
