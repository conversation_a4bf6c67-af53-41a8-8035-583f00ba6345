#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
import traceback
from datetime import datetime

def test_maintenance_save_direct():
    """Testa o salvamento direto sem interface gráfica"""
    
    print("=== TESTE DIRETO DE SALVAMENTO (SEM GUI) ===")
    
    # Inicializar database
    db_manager = DatabaseManager()
    
    try:
        # Verificar usuário
        users = db_manager.execute_query("SELECT * FROM users LIMIT 1")
        if not users:
            print("❌ Nenhum usuário encontrado")
            return
        
        user_id = users[0]['id']
        print(f"✓ Usuário encontrado: ID {user_id}")
        
        # Verificar veículos
        vehicles = db_manager.get_user_vehicles(user_id)
        if not vehicles:
            print("❌ Nenhum veículo encontrado")
            return
        
        vehicle = vehicles[0]
        print(f"✓ Veículo encontrado: {vehicle['name']}")
        
        # Simular dados do formulário
        maintenance_data = {
            'vehicle_id': vehicle['id'],
            'maintenance_type': 'oil_change',
            'description': 'Teste direto de troca de óleo',
            'service_date': '2025-07-17',  # Formato ISO
            'mileage_at_service': 25000,
            'cost': 180.50,
            'service_provider': 'Oficina Teste Direto',
            'next_service_date': None,
            'next_service_mileage': None,
            'warranty_period': None,
            'warranty_expiry': None,
            'receipt_number': 'REC-DIRETO-001',
            'notes': 'Teste direto do sistema',
            'is_scheduled': False,
            'is_completed': True
        }
        
        print("🔄 Dados preparados:")
        for key, value in maintenance_data.items():
            print(f"   {key}: {value}")
        
        # Contar manutenções antes
        count_before = db_manager.execute_query(
            "SELECT COUNT(*) as count FROM vehicle_maintenance WHERE user_id = ?",
            (user_id,)
        )[0]['count']
        print(f"📊 Manutenções antes: {count_before}")
        
        # Tentar salvar
        print("🔄 Salvando manutenção...")
        maintenance_id = db_manager.add_maintenance_record(user_id, maintenance_data)
        print(f"✅ Manutenção salva com ID: {maintenance_id}")
        
        # Contar manutenções depois
        count_after = db_manager.execute_query(
            "SELECT COUNT(*) as count FROM vehicle_maintenance WHERE user_id = ?",
            (user_id,)
        )[0]['count']
        print(f"📊 Manutenções depois: {count_after}")
        
        if count_after > count_before:
            print("✅ SALVAMENTO DIRETO FUNCIONOU!")
        else:
            print("❌ SALVAMENTO DIRETO FALHOU!")
        
        # Verificar dados salvos
        saved = db_manager.execute_query(
            "SELECT * FROM vehicle_maintenance WHERE id = ?",
            (maintenance_id,)
        )[0]
        
        print("📋 Dados verificados no banco:")
        print(f"   ID: {saved['id']}")
        print(f"   Veículo: {saved['vehicle_id']}")
        print(f"   Tipo: {saved['maintenance_type']}")
        print(f"   Descrição: {saved['description']}")
        print(f"   Data: {saved['service_date']}")
        print(f"   Custo: {saved['cost']}")
        
    except Exception as e:
        print(f"\n❌ ERRO NO TESTE DIRETO:")
        print(f"Tipo: {type(e).__name__}")
        print(f"Mensagem: {str(e)}")
        traceback.print_exc()

def test_gui_validation_logic():
    """Testa a lógica de validação do formulário"""
    
    print("\n=== TESTE DA LÓGICA DE VALIDAÇÃO ===")
    
    # Simular dados do formulário
    test_cases = [
        {
            'name': 'Dados válidos',
            'vehicle': 'Carro Teste',
            'maintenance_type': 'oil_change',
            'description': 'Troca de óleo',
            'service_date': '17/07/2025',
            'cost': '180.50',
            'expected': True
        },
        {
            'name': 'Veículo vazio',
            'vehicle': '',
            'maintenance_type': 'oil_change',
            'description': 'Troca de óleo',
            'service_date': '17/07/2025',
            'cost': '180.50',
            'expected': False
        },
        {
            'name': 'Tipo vazio',
            'vehicle': 'Carro Teste',
            'maintenance_type': '',
            'description': 'Troca de óleo',
            'service_date': '17/07/2025',
            'cost': '180.50',
            'expected': False
        },
        {
            'name': 'Descrição vazia',
            'vehicle': 'Carro Teste',
            'maintenance_type': 'oil_change',
            'description': '',
            'service_date': '17/07/2025',
            'cost': '180.50',
            'expected': False
        },
        {
            'name': 'Data vazia',
            'vehicle': 'Carro Teste',
            'maintenance_type': 'oil_change',
            'description': 'Troca de óleo',
            'service_date': '',
            'cost': '180.50',
            'expected': False
        },
        {
            'name': 'Custo vazio',
            'vehicle': 'Carro Teste',
            'maintenance_type': 'oil_change',
            'description': 'Troca de óleo',
            'service_date': '17/07/2025',
            'cost': '',
            'expected': False
        },
        {
            'name': 'Custo inválido',
            'vehicle': 'Carro Teste',
            'maintenance_type': 'oil_change',
            'description': 'Troca de óleo',
            'service_date': '17/07/2025',
            'cost': 'abc',
            'expected': False
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Testando: {test_case['name']}")
        
        # Simular validação
        valid = True
        
        if not test_case['vehicle']:
            valid = False
            print("   ❌ Veículo vazio")
        
        if not test_case['maintenance_type']:
            valid = False
            print("   ❌ Tipo de manutenção vazio")
        
        if not test_case['description'].strip():
            valid = False
            print("   ❌ Descrição vazia")
        
        if not test_case['service_date']:
            valid = False
            print("   ❌ Data vazia")
        
        if not test_case['cost']:
            valid = False
            print("   ❌ Custo vazio")
        else:
            try:
                float(test_case['cost'])
            except ValueError:
                valid = False
                print("   ❌ Custo inválido")
        
        if valid == test_case['expected']:
            print(f"   ✅ Resultado esperado: {test_case['expected']}")
        else:
            print(f"   ❌ Resultado inesperado. Esperado: {test_case['expected']}, Obtido: {valid}")

if __name__ == "__main__":
    test_maintenance_save_direct()
    test_gui_validation_logic()
