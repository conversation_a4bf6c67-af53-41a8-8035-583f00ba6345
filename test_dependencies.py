#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import traceback

def test_dependencies():
    """Testa as dependências necessárias para o formulário de manutenção"""
    
    print("=== TESTE DE DEPENDÊNCIAS ===")
    
    # Testar tkinter
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox
        print("✓ tkinter importado com sucesso")
    except ImportError as e:
        print(f"❌ Erro ao importar tkinter: {e}")
        return False
    
    # Testar datetime
    try:
        from datetime import datetime, date
        print("✓ datetime importado com sucesso")
    except ImportError as e:
        print(f"❌ Erro ao importar datetime: {e}")
        return False
    
    # Testar tkcalendar
    try:
        from tkcalendar import DateEntry
        print("✓ tkcalendar importado com sucesso")
    except ImportError as e:
        print(f"❌ Erro ao importar tkcalendar: {e}")
        print("   Isso pode causar problemas no formulário de manutenção")
        print("   Instale com: pip install tkcalendar")
        return False
    
    # Testar importação do database
    try:
        sys.path.append('src')
        from database import DatabaseManager
        print("✓ DatabaseManager importado com sucesso")
    except ImportError as e:
        print(f"❌ Erro ao importar DatabaseManager: {e}")
        return False
    
    # Testar importação do formulário de manutenção
    try:
        from gui.maintenance_form import MaintenanceForm
        print("✓ MaintenanceForm importado com sucesso")
    except ImportError as e:
        print(f"❌ Erro ao importar MaintenanceForm: {e}")
        traceback.print_exc()
        return False
    
    print("\n✅ TODAS AS DEPENDÊNCIAS ESTÃO OK!")
    return True

def test_simple_maintenance_form():
    """Testa criação básica do formulário"""
    
    if not test_dependencies():
        return
    
    print("\n=== TESTE DE CRIAÇÃO DO FORMULÁRIO ===")
    
    try:
        import tkinter as tk
        sys.path.append('src')
        from database import DatabaseManager
        from gui.maintenance_form import MaintenanceForm
        
        # Inicializar database
        db_manager = DatabaseManager()
        
        # Verificar usuário
        users = db_manager.execute_query("SELECT * FROM users LIMIT 1")
        if not users:
            print("❌ Nenhum usuário encontrado")
            return
        
        user_id = users[0]['id']
        
        # Criar janela principal
        root = tk.Tk()
        root.withdraw()  # Esconder janela principal
        
        # Tentar criar formulário
        form = MaintenanceForm(root, db_manager, user_id)
        print("✓ Formulário criado com sucesso")
        
        # Verificar se os campos foram criados
        if hasattr(form, 'vehicle_var'):
            print("✓ Campo veículo criado")
        else:
            print("❌ Campo veículo não encontrado")
        
        if hasattr(form, 'maintenance_type_var'):
            print("✓ Campo tipo de manutenção criado")
        else:
            print("❌ Campo tipo de manutenção não encontrado")
        
        if hasattr(form, 'cost_var'):
            print("✓ Campo custo criado")
        else:
            print("❌ Campo custo não encontrado")
        
        # Fechar formulário
        form.window.destroy()
        root.destroy()
        
        print("✅ TESTE DO FORMULÁRIO CONCLUÍDO COM SUCESSO!")
        
    except Exception as e:
        print(f"❌ ERRO NO TESTE DO FORMULÁRIO:")
        print(f"Tipo: {type(e).__name__}")
        print(f"Mensagem: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_maintenance_form()
