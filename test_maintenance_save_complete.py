#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import tkinter as tk
from tkinter import messagebox
from database import DatabaseManager
from gui.maintenance_form import MaintenanceForm
import traceback
from datetime import datetime

def test_complete_maintenance_save():
    """Testa o processo completo de salvamento de manutenção"""
    
    print("=== TESTE COMPLETO DE SALVAMENTO DE MANUTENÇÃO ===")
    
    # Inicializar database
    db_manager = DatabaseManager()
    
    try:
        # Verificar usuário
        users = db_manager.execute_query("SELECT * FROM users LIMIT 1")
        if not users:
            print("❌ Nenhum usuário encontrado. Criando usuário de teste...")
            db_manager.execute_query(
                "INSERT INTO users (username, password_hash, email) VALUES (?, ?, ?)",
                ("teste_completo", "hash_teste", "<EMAIL>")
            )
            users = db_manager.execute_query("SELECT * FROM users LIMIT 1")
        
        user_id = users[0]['id']
        print(f"✓ Usuário encontrado: ID {user_id}")
        
        # Verificar veículos
        vehicles = db_manager.get_user_vehicles(user_id)
        if not vehicles:
            print("❌ Nenhum veículo encontrado. Criando veículo de teste...")
            vehicle_data = {
                'name': 'Carro Teste Completo',
                'brand': 'Volkswagen',
                'model': 'Golf',
                'year': 2022,
                'license_plate': 'CMP-5678',
                'color': 'Vermelho',
                'fuel_type': 'flex',
                'engine_size': '1.4',
                'mileage': 25000
            }
            db_manager.add_vehicle(user_id, vehicle_data)
            vehicles = db_manager.get_user_vehicles(user_id)
        
        vehicle = vehicles[0]
        print(f"✓ Veículo encontrado: {vehicle['name']} - {vehicle['brand']} {vehicle['model']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.withdraw()  # Esconder janela principal
        
        # Variável para capturar se o callback foi executado
        callback_executed = False
        
        def test_callback():
            nonlocal callback_executed
            callback_executed = True
            print("✅ Callback executado com sucesso!")
        
        # Criar formulário
        print("🔄 Criando formulário de manutenção...")
        form = MaintenanceForm(root, db_manager, user_id, callback=test_callback)
        
        # Simular preenchimento dos campos obrigatórios
        print("🔄 Preenchendo campos obrigatórios...")
        
        # Selecionar veículo
        vehicle_name = f"{vehicle['name']} - {vehicle['brand']} {vehicle['model']}"
        if vehicle_name in form.vehicle_options:
            form.vehicle_var.set(vehicle_name)
            print(f"✓ Veículo selecionado: {vehicle_name}")
        else:
            print(f"❌ Veículo não encontrado nas opções: {list(form.vehicle_options.keys())}")
            return
        
        # Selecionar tipo de manutenção
        form.maintenance_type_var.set('oil_change')
        print("✓ Tipo de manutenção selecionado: oil_change")
        
        # Preencher descrição
        form.description_text.insert('1.0', 'Teste completo de troca de óleo')
        print("✓ Descrição preenchida")
        
        # Preencher data do serviço
        today = datetime.now().strftime('%d/%m/%Y')
        form.service_date_var.set(today)
        print(f"✓ Data do serviço preenchida: {today}")
        
        # Preencher custo
        form.cost_var.set('180.50')
        print("✓ Custo preenchido: R$ 180,50")
        
        # Preencher campos opcionais
        form.mileage_var.set('25000')
        form.service_provider_var.set('Oficina Teste Completo')
        form.receipt_number_var.set('REC-COMPLETO-001')
        form.notes_text.insert('1.0', 'Teste completo do sistema de manutenção')
        
        print("✓ Campos opcionais preenchidos")
        
        # Contar manutenções antes do salvamento
        maintenance_before = db_manager.execute_query(
            "SELECT COUNT(*) as count FROM vehicle_maintenance WHERE user_id = ?",
            (user_id,)
        )[0]['count']
        print(f"📊 Manutenções antes do salvamento: {maintenance_before}")
        
        # Simular clique no botão salvar
        print("🔄 Executando salvamento...")
        
        # Chamar método save diretamente
        form.save()
        
        # Verificar se a manutenção foi salva
        maintenance_after = db_manager.execute_query(
            "SELECT COUNT(*) as count FROM vehicle_maintenance WHERE user_id = ?",
            (user_id,)
        )[0]['count']
        print(f"📊 Manutenções após o salvamento: {maintenance_after}")
        
        if maintenance_after > maintenance_before:
            print("✅ Manutenção salva com sucesso no banco de dados!")
            
            # Verificar dados salvos
            last_maintenance = db_manager.execute_query(
                "SELECT * FROM vehicle_maintenance WHERE user_id = ? ORDER BY id DESC LIMIT 1",
                (user_id,)
            )[0]
            
            print("📋 Dados salvos:")
            print(f"   ID: {last_maintenance['id']}")
            print(f"   Veículo ID: {last_maintenance['vehicle_id']}")
            print(f"   Tipo: {last_maintenance['maintenance_type']}")
            print(f"   Descrição: {last_maintenance['description']}")
            print(f"   Custo: R$ {last_maintenance['cost']}")
            print(f"   Data: {last_maintenance['service_date']}")
            
        else:
            print("❌ Manutenção NÃO foi salva no banco de dados!")
        
        # Verificar se callback foi executado
        if callback_executed:
            print("✅ Callback foi executado corretamente!")
        else:
            print("❌ Callback NÃO foi executado!")
        
        # Limpar
        root.destroy()
        
        print("\n✅ TESTE COMPLETO FINALIZADO!")
        
    except Exception as e:
        print(f"\n❌ ERRO DURANTE O TESTE COMPLETO:")
        print(f"Tipo do erro: {type(e).__name__}")
        print(f"Mensagem: {str(e)}")
        print("\nTraceback completo:")
        traceback.print_exc()

if __name__ == "__main__":
    test_complete_maintenance_save()
