#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import tkinter as tk
from tkinter import messagebox
from database import DatabaseManager
from gui.maintenance_form import MaintenanceForm
import traceback

def test_maintenance_gui():
    """Testa a interface gráfica de manutenção"""
    
    print("=== TESTE DA INTERFACE DE MANUTENÇÃO ===")
    
    # Inicializar database
    db_manager = DatabaseManager()
    
    try:
        # Verificar usuário
        users = db_manager.execute_query("SELECT * FROM users LIMIT 1")
        if not users:
            print("❌ Nenhum usuário encontrado. Criando usuário de teste...")
            db_manager.execute_query(
                "INSERT INTO users (username, password_hash, email) VALUES (?, ?, ?)",
                ("teste", "hash_teste", "<EMAIL>")
            )
            users = db_manager.execute_query("SELECT * FROM users LIMIT 1")
        
        user_id = users[0]['id']
        print(f"✓ Usuário encontrado: ID {user_id}")
        
        # Verificar veículos
        vehicles = db_manager.get_user_vehicles(user_id)
        if not vehicles:
            print("❌ Nenhum veículo encontrado. Criando veículo de teste...")
            vehicle_data = {
                'name': 'Carro GUI Teste',
                'brand': 'Honda',
                'model': 'Civic',
                'year': 2021,
                'license_plate': 'GUI-1234',
                'color': 'Azul',
                'fuel_type': 'flex',
                'engine_size': '2.0',
                'mileage': 30000
            }
            db_manager.add_vehicle(user_id, vehicle_data)
            vehicles = db_manager.get_user_vehicles(user_id)
        
        print(f"✓ Veículo encontrado: {vehicles[0]['name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Manutenção GUI")
        root.geometry("400x300")
        
        def callback_success():
            print("✅ Callback executado com sucesso!")
            messagebox.showinfo("Sucesso", "Manutenção salva e callback executado!")
        
        def open_maintenance_form():
            try:
                print("🔄 Abrindo formulário de manutenção...")
                MaintenanceForm(root, db_manager, user_id, callback=callback_success)
            except Exception as e:
                print(f"❌ Erro ao abrir formulário: {e}")
                traceback.print_exc()
                messagebox.showerror("Erro", f"Erro ao abrir formulário: {str(e)}")
        
        # Botão para abrir formulário
        btn_frame = tk.Frame(root)
        btn_frame.pack(expand=True)
        
        tk.Label(btn_frame, text="Teste da Interface de Manutenção", 
                font=('Arial', 14, 'bold')).pack(pady=20)
        
        tk.Button(btn_frame, text="Abrir Formulário de Manutenção", 
                 command=open_maintenance_form, 
                 font=('Arial', 12),
                 bg='#4CAF50', fg='white',
                 padx=20, pady=10).pack(pady=10)
        
        tk.Label(btn_frame, text="Clique no botão acima para testar\no formulário de manutenção", 
                font=('Arial', 10)).pack(pady=10)
        
        tk.Button(btn_frame, text="Fechar", 
                 command=root.quit,
                 font=('Arial', 10),
                 padx=20, pady=5).pack(pady=10)
        
        print("✓ Interface criada. Aguardando interação do usuário...")
        print("📝 Instruções:")
        print("   1. Clique em 'Abrir Formulário de Manutenção'")
        print("   2. Preencha os campos obrigatórios")
        print("   3. Clique em 'Salvar'")
        print("   4. Verifique se aparece mensagem de sucesso")
        
        root.mainloop()
        
    except Exception as e:
        print(f"\n❌ ERRO DURANTE O TESTE:")
        print(f"Tipo do erro: {type(e).__name__}")
        print(f"Mensagem: {str(e)}")
        print("\nTraceback completo:")
        traceback.print_exc()

if __name__ == "__main__":
    test_maintenance_gui()
