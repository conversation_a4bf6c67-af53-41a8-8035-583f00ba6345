#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Formulário para gerenciamento de manutenção de veículos
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from tkcalendar import DateEntry

class MaintenanceForm:
    def __init__(self, parent, db_manager, user_id, vehicle_id=None, maintenance_data=None, callback=None):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.vehicle_id = vehicle_id
        self.maintenance_data = maintenance_data
        self.callback = callback
        self.is_edit_mode = maintenance_data is not None
        
        # Criar janela
        self.window = tk.Toplevel(parent)
        self.window.title("Editar Manutenção" if self.is_edit_mode else "Nova Manutenção")
        self.window.geometry("600x800")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        # Centralizar janela
        self.center_window()
        
        # Carregar veículos
        self.load_vehicles()
        
        # Criar interface
        self.create_widgets()
        
        # Preencher dados se for edição
        if self.is_edit_mode:
            self.populate_fields()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.window.winfo_screenheight() // 2) - (800 // 2)
        self.window.geometry(f"600x800+{x}+{y}")
    
    def load_vehicles(self):
        """Carrega lista de veículos do usuário"""
        try:
            self.vehicles = self.db_manager.get_user_vehicles(self.user_id)
            self.vehicle_options = {f"{v['name']} - {v['brand']} {v['model']}": v['id'] for v in self.vehicles}
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar veículos: {str(e)}")
            self.vehicles = []
            self.vehicle_options = {}
    
    def create_widgets(self):
        """Cria os widgets do formulário"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title = ttk.Label(main_frame, text="Editar Manutenção" if self.is_edit_mode else "Nova Manutenção",
                         font=('Arial', 14, 'bold'))
        title.pack(pady=(0, 20))
        
        # Frame para campos
        fields_frame = ttk.Frame(main_frame)
        fields_frame.pack(fill=tk.BOTH, expand=True)
        
        # Veículo
        ttk.Label(fields_frame, text="Veículo:*").grid(row=0, column=0, sticky='w', pady=5)
        self.vehicle_var = tk.StringVar()
        vehicle_combo = ttk.Combobox(fields_frame, textvariable=self.vehicle_var, width=37, state='readonly')
        vehicle_combo['values'] = list(self.vehicle_options.keys())
        vehicle_combo.grid(row=0, column=1, sticky='ew', pady=5)
        
        # Pré-selecionar veículo se fornecido
        if self.vehicle_id:
            for name, vid in self.vehicle_options.items():
                if vid == self.vehicle_id:
                    self.vehicle_var.set(name)
                    break
        
        # Tipo de manutenção
        ttk.Label(fields_frame, text="Tipo de Manutenção:*").grid(row=1, column=0, sticky='w', pady=5)
        self.maintenance_type_var = tk.StringVar()
        type_combo = ttk.Combobox(fields_frame, textvariable=self.maintenance_type_var, width=37, state='readonly')
        type_combo['values'] = ('oil_change', 'tire_change', 'brake_service', 'general_service', 'repair')
        type_combo.grid(row=1, column=1, sticky='ew', pady=5)
        
        # Descrição
        ttk.Label(fields_frame, text="Descrição:*").grid(row=2, column=0, sticky='nw', pady=5)
        self.description_text = tk.Text(fields_frame, height=3, width=40)
        self.description_text.grid(row=2, column=1, sticky='ew', pady=5)
        
        # Data do serviço
        ttk.Label(fields_frame, text="Data do Serviço:*").grid(row=3, column=0, sticky='w', pady=5)
        self.service_date_var = tk.StringVar()
        try:
            self.service_date_entry = DateEntry(fields_frame, textvariable=self.service_date_var,
                                               width=37, background='darkblue',
                                               foreground='white', borderwidth=2, date_pattern='dd/mm/yyyy')
            self.service_date_entry.grid(row=3, column=1, sticky='ew', pady=5)
        except:
            ttk.Entry(fields_frame, textvariable=self.service_date_var, width=40).grid(row=3, column=1, sticky='ew', pady=5)
        
        # Quilometragem no serviço
        ttk.Label(fields_frame, text="Quilometragem:").grid(row=4, column=0, sticky='w', pady=5)
        self.mileage_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.mileage_var, width=40).grid(row=4, column=1, sticky='ew', pady=5)
        
        # Custo
        ttk.Label(fields_frame, text="Custo:*").grid(row=5, column=0, sticky='w', pady=5)
        self.cost_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.cost_var, width=40).grid(row=5, column=1, sticky='ew', pady=5)
        
        # Prestador de serviço
        ttk.Label(fields_frame, text="Prestador de Serviço:").grid(row=6, column=0, sticky='w', pady=5)
        self.service_provider_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.service_provider_var, width=40).grid(row=6, column=1, sticky='ew', pady=5)
        
        # Próxima manutenção - data
        ttk.Label(fields_frame, text="Próxima Manutenção:").grid(row=7, column=0, sticky='w', pady=5)
        self.next_service_date_var = tk.StringVar()
        try:
            self.next_service_date_entry = DateEntry(fields_frame, textvariable=self.next_service_date_var,
                                                    width=37, background='darkblue',
                                                    foreground='white', borderwidth=2, date_pattern='dd/mm/yyyy')
            self.next_service_date_entry.grid(row=7, column=1, sticky='ew', pady=5)
        except:
            ttk.Entry(fields_frame, textvariable=self.next_service_date_var, width=40).grid(row=7, column=1, sticky='ew', pady=5)
        
        # Próxima manutenção - quilometragem
        ttk.Label(fields_frame, text="Próxima Km:").grid(row=8, column=0, sticky='w', pady=5)
        self.next_mileage_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.next_mileage_var, width=40).grid(row=8, column=1, sticky='ew', pady=5)
        
        # Período de garantia (meses)
        ttk.Label(fields_frame, text="Garantia (meses):").grid(row=9, column=0, sticky='w', pady=5)
        self.warranty_period_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.warranty_period_var, width=40).grid(row=9, column=1, sticky='ew', pady=5)
        
        # Data de vencimento da garantia
        ttk.Label(fields_frame, text="Vencimento Garantia:").grid(row=10, column=0, sticky='w', pady=5)
        self.warranty_expiry_var = tk.StringVar()
        try:
            self.warranty_expiry_entry = DateEntry(fields_frame, textvariable=self.warranty_expiry_var,
                                                  width=37, background='darkblue',
                                                  foreground='white', borderwidth=2, date_pattern='dd/mm/yyyy')
            self.warranty_expiry_entry.grid(row=10, column=1, sticky='ew', pady=5)
        except:
            ttk.Entry(fields_frame, textvariable=self.warranty_expiry_var, width=40).grid(row=10, column=1, sticky='ew', pady=5)
        
        # Número do recibo
        ttk.Label(fields_frame, text="Nº Recibo:").grid(row=11, column=0, sticky='w', pady=5)
        self.receipt_number_var = tk.StringVar()
        ttk.Entry(fields_frame, textvariable=self.receipt_number_var, width=40).grid(row=11, column=1, sticky='ew', pady=5)
        
        # Checkboxes
        checkbox_frame = ttk.Frame(fields_frame)
        checkbox_frame.grid(row=12, column=1, sticky='w', pady=10)
        
        self.is_scheduled_var = tk.BooleanVar()
        ttk.Checkbutton(checkbox_frame, text="Manutenção Agendada", 
                       variable=self.is_scheduled_var).pack(anchor='w')
        
        self.is_completed_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(checkbox_frame, text="Manutenção Concluída", 
                       variable=self.is_completed_var).pack(anchor='w')
        
        # Observações
        ttk.Label(fields_frame, text="Observações:").grid(row=13, column=0, sticky='nw', pady=5)
        self.notes_text = tk.Text(fields_frame, height=4, width=40)
        self.notes_text.grid(row=13, column=1, sticky='ew', pady=5)
        
        # Configurar grid
        fields_frame.columnconfigure(1, weight=1)
        
        # Frame para botões
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Botões
        ttk.Button(buttons_frame, text="Cancelar", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Salvar", command=self.save).pack(side=tk.RIGHT)
    
    def populate_fields(self):
        """Preenche os campos com dados da manutenção para edição"""
        if not self.maintenance_data:
            return
        
        # Selecionar veículo
        for name, vid in self.vehicle_options.items():
            if vid == self.maintenance_data['vehicle_id']:
                self.vehicle_var.set(name)
                break

        self.maintenance_type_var.set(self.maintenance_data['maintenance_type'] or '')

        if self.maintenance_data['description']:
            self.description_text.insert('1.0', self.maintenance_data['description'])

        if self.maintenance_data['service_date']:
            self.service_date_var.set(self.maintenance_data['service_date'])

        if self.maintenance_data['mileage_at_service']:
            self.mileage_var.set(str(self.maintenance_data['mileage_at_service']))
        
        if self.maintenance_data.get('cost'):
            self.cost_var.set(str(self.maintenance_data['cost']))
        
        self.service_provider_var.set(self.maintenance_data.get('service_provider', ''))
        
        if self.maintenance_data.get('next_service_date'):
            self.next_service_date_var.set(self.maintenance_data['next_service_date'])
        
        if self.maintenance_data.get('next_service_mileage'):
            self.next_mileage_var.set(str(self.maintenance_data['next_service_mileage']))
        
        if self.maintenance_data['warranty_period']:
            self.warranty_period_var.set(str(self.maintenance_data['warranty_period']))

        if self.maintenance_data['warranty_expiry']:
            self.warranty_expiry_var.set(self.maintenance_data['warranty_expiry'])

        self.receipt_number_var.set(self.maintenance_data['receipt_number'] or '')

        self.is_scheduled_var.set(self.maintenance_data['is_scheduled'] or False)
        self.is_completed_var.set(self.maintenance_data['is_completed'] if self.maintenance_data['is_completed'] is not None else True)

        if self.maintenance_data['notes']:
            self.notes_text.insert('1.0', self.maintenance_data['notes'])
    
    def validate_fields(self):
        """Valida os campos obrigatórios"""
        if not self.vehicle_var.get():
            messagebox.showerror("Erro", "Selecione um veículo!")
            return False
        
        if not self.maintenance_type_var.get():
            messagebox.showerror("Erro", "Selecione o tipo de manutenção!")
            return False
        
        if not self.description_text.get('1.0', tk.END).strip():
            messagebox.showerror("Erro", "Descrição é obrigatória!")
            return False
        
        if not self.service_date_var.get():
            messagebox.showerror("Erro", "Data do serviço é obrigatória!")
            return False
        
        if not self.cost_var.get():
            messagebox.showerror("Erro", "Custo é obrigatório!")
            return False
        
        try:
            float(self.cost_var.get())
        except ValueError:
            messagebox.showerror("Erro", "Custo deve ser um número válido!")
            return False
        
        return True
    
    def save(self):
        """Salva a manutenção"""
        if not self.validate_fields():
            return

        try:
            # Obter ID do veículo selecionado
            selected_vehicle = self.vehicle_var.get()
            vehicle_id = self.vehicle_options.get(selected_vehicle)

            if not vehicle_id:
                messagebox.showerror("Erro", "Veículo selecionado inválido!")
                return

            # Converter data do formato brasileiro para ISO se necessário
            service_date = self.service_date_var.get()
            if service_date:
                try:
                    # Tentar converter de dd/mm/yyyy para yyyy-mm-dd
                    if '/' in service_date:
                        day, month, year = service_date.split('/')
                        service_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                except:
                    pass  # Manter formato original se conversão falhar

            # Converter next_service_date se necessário
            next_service_date = self.next_service_date_var.get() or None
            if next_service_date and '/' in next_service_date:
                try:
                    day, month, year = next_service_date.split('/')
                    next_service_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                except:
                    pass

            # Converter warranty_expiry se necessário
            warranty_expiry = self.warranty_expiry_var.get() or None
            if warranty_expiry and '/' in warranty_expiry:
                try:
                    day, month, year = warranty_expiry.split('/')
                    warranty_expiry = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                except:
                    pass

            # Preparar dados
            maintenance_data = {
                'vehicle_id': vehicle_id,
                'maintenance_type': self.maintenance_type_var.get(),
                'description': self.description_text.get('1.0', tk.END).strip(),
                'service_date': service_date,
                'mileage_at_service': int(self.mileage_var.get()) if self.mileage_var.get() else None,
                'cost': float(self.cost_var.get()),
                'service_provider': self.service_provider_var.get().strip() or None,
                'next_service_date': next_service_date,
                'next_service_mileage': int(self.next_mileage_var.get()) if self.next_mileage_var.get() else None,
                'warranty_period': int(self.warranty_period_var.get()) if self.warranty_period_var.get() else None,
                'warranty_expiry': warranty_expiry,
                'receipt_number': self.receipt_number_var.get().strip() or None,
                'is_scheduled': self.is_scheduled_var.get(),
                'is_completed': self.is_completed_var.get(),
                'notes': self.notes_text.get('1.0', tk.END).strip() or None
            }
            
            if self.is_edit_mode:
                # Atualizar manutenção existente
                print(f"[DEBUG] Atualizando manutenção ID: {self.maintenance_data['id']}")
                self.db_manager.update_maintenance_record(self.maintenance_data['id'], maintenance_data)
                messagebox.showinfo("Sucesso", "Manutenção atualizada com sucesso!")
            else:
                # Criar nova manutenção
                print(f"[DEBUG] Criando nova manutenção para usuário: {self.user_id}")
                print(f"[DEBUG] Dados: {maintenance_data}")
                maintenance_id = self.db_manager.add_maintenance_record(self.user_id, maintenance_data)
                print(f"[DEBUG] Manutenção criada com ID: {maintenance_id}")
                messagebox.showinfo("Sucesso", "Manutenção registrada com sucesso!")

            # Chamar callback se fornecido
            if self.callback:
                print("[DEBUG] Executando callback...")
                self.callback()

            # Fechar janela
            print("[DEBUG] Fechando janela...")
            self.window.destroy()

        except ValueError as e:
            print(f"[ERROR] Erro de valor: {str(e)}")
            messagebox.showerror("Erro", f"Dados inválidos: {str(e)}")
        except Exception as e:
            print(f"[ERROR] Erro geral: {str(e)}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("Erro", f"Erro ao salvar manutenção: {str(e)}")
    
    def cancel(self):
        """Cancela a operação"""
        self.window.destroy()
